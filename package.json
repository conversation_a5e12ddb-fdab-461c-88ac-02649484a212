{"name": "expense-processing-service", "version": "1.0.0", "description": "NestJS Service for Expense Document Processing with BullMQ", "author": "Expense Processing Team", "private": true, "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "validate-timing": "node scripts/validate-timing.js", "test-parallel": "node scripts/test-parallel-processing.js", "test-timing-fix": "node scripts/test-timing-fix.js", "fix-timing": "node scripts/fix-existing-timing.js", "validate-pdf": "node scripts/validate-pdf.js"}, "dependencies": {"@aws-sdk/client-bedrock-runtime": "^3.857.0", "@aws-sdk/client-textract": "^3.848.0", "@aws-sdk/types": "^3.840.0", "@llamaindex/anthropic": "^0.3.19", "@nestjs/bull": "^10.0.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.1.17", "@nestjs/throttler": "^5.0.1", "axios": "^1.6.2", "bull": "^4.11.3", "bullmq": "^4.15.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "dotenv": "^16.3.1", "ioredis": "^5.3.2", "llamaindex": "^0.7.0", "multer": "^1.4.5-lts.1", "p-limit": "^3.1.0", "pdf-lib": "^1.17.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1", "zod": "^3.25.76"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}