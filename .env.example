# Application
NODE_ENV=development
PORT=3000

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Anthropic (Fallback)
ANTHROPIC_KEY=your_anthropic_api_key_here

# LlamaIndex
LLAMAINDEX_API_KEY=your_llamaindex_api_key_here

# AWS Bedrock Configuration (Primary AI Provider)
BEDROCK_AWS_ACCESS_KEY_ID=your_bedrock_aws_access_key_id_here
BEDROCK_AWS_SECRET_ACCESS_KEY=your_bedrock_aws_secret_access_key_here
BEDROCK_AWS_SESSION_TOKEN=your_bedrock_aws_session_token_here
BEDROCK_AWS_REGION=us-east-1
BEDROCK_MODEL=eu.amazon.nova-pro-v1:0

# Citation Model (Optimized for structured output)
CITATION_MODEL=amazon.nova-micro-v1:0

# AWS Textract Configuration (Document Processing)
TEXTRACT_AWS_ACCESS_KEY_ID=your_textract_aws_access_key_id_here
TEXTRACT_AWS_SECRET_ACCESS_KEY=your_textract_aws_secret_access_key_here
TEXTRACT_AWS_REGION=your_textract_aws_region_here

# S3 Bucket for Textract async operations (multi-page PDFs)
TEXTRACT_S3_BUCKET=your-textract-temp-bucket

# Document Reader Configuration
DOCUMENT_READER=llamaparse  # Options: llamaparse, textract

# File Upload
MAX_FILE_SIZE=50MB
UPLOAD_PATH=./uploads

# Job Queue Configuration
QUEUE_CONCURRENCY=10
MAX_RETRY_ATTEMPTS=3
JOB_TIMEOUT=300000

# Processing Optimization
USE_PARALLEL_PROCESSING=true

# Monitoring
ENABLE_SWAGGER=true
ENABLE_THROTTLING=true
THROTTLE_TTL=60
THROTTLE_LIMIT=100
