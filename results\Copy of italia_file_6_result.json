{"image_quality_assessment": {"image_path": "uploads\\v4_run_016_Copy of italia_file_6.pdf", "assessment_method": "LLM", "model_used": "eu.amazon.nova-pro-v1:0", "timestamp": "2025-07-31T18:40:07.277Z", "quality_score": 60, "quality_level": "good", "suitable_for_extraction": true, "blur_detection": {"detected": true, "severity_level": "medium", "confidence_score": 0.8, "quantitative_measure": 0.5, "description": "The text and edges show moderate blur, likely due to low resolution or scanning quality.", "recommendation": "Enhance image resolution or re-scan the document for better clarity."}, "contrast_assessment": {"detected": true, "severity_level": "low", "confidence_score": 0.7, "quantitative_measure": 0.7, "description": "The contrast between text and background is adequate but could be improved for better readability.", "recommendation": "Adjust contrast settings or use a higher-quality scan."}, "glare_identification": {"detected": false, "severity_level": "none", "confidence_score": 0.9, "quantitative_measure": 0, "description": "No glare or overexposed regions are detected in the image.", "recommendation": "No action needed."}, "water_stains": {"detected": false, "severity_level": "none", "confidence_score": 0.9, "quantitative_measure": 0, "description": "There are no visible water stains or discolorations on the document.", "recommendation": "No action needed."}, "tears_or_folds": {"detected": false, "severity_level": "none", "confidence_score": 0.9, "quantitative_measure": 0, "description": "No tears, folds, or creases are visible in the document.", "recommendation": "No action needed."}, "cut_off_detection": {"detected": false, "severity_level": "none", "confidence_score": 0.9, "quantitative_measure": 0, "description": "No edges of the document appear to be cut off.", "recommendation": "No action needed."}, "missing_sections": {"detected": false, "severity_level": "none", "confidence_score": 0.9, "quantitative_measure": 0, "description": "All sections of the document appear to be fully captured.", "recommendation": "No action needed."}, "obstructions": {"detected": false, "severity_level": "none", "confidence_score": 0.9, "quantitative_measure": 0, "description": "There are no obstructions or shadows blocking the document content.", "recommendation": "No action needed."}, "overall_quality_score": 6}, "classification": {"is_expense": true, "expense_type": "flights", "language": "English", "language_confidence": 95, "document_location": "Italy", "expected_location": "Italy", "location_match": true, "error_type": null, "error_message": null, "classification_confidence": 90, "reasoning": "The document contains multiple fields that align with the expense schema, including supplier, consumer, transaction amount, transaction date, invoice number, tax information, payment method, and item description. The presence of these fields, along with the detailed breakdown of the fare and taxes, confirms it as an expense document related to a flight.", "schema_field_analysis": {"fields_found": ["supplier", "consumerRecipient", "transactionAmount", "transactionDate", "invoiceReceiptNumber", "taxInformation", "paymentMethod", "itemDescriptionLineItems"], "fields_missing": [], "total_fields_found": 8, "expense_identification_reasoning": "All required fields for expense identification are present, including detailed transaction information, taxes, and payment method."}}, "extraction": {"customer_name_on_invoice": null, "customer_address_on_invoice": null, "customer_vat_number_on_invoice": null, "customer_tax_code_on_invoice": null, "currency": "EUR", "amount": 460.86, "receipt_type": "Receipt", "receipt_quality": null, "payment_method": "CC VI XXXXXXXXXXXX2982 ExpXXXX S385619", "vehicle_make_model": null, "vehicle_fuel_type": null, "distance_traveled": null, "route_documentation": null, "car_registration": null, "personal_information": null, "business_trip_reporting": null, "per_diem_method": null, "name": "Papadia Fabio <PERSON> (ADT)", "ticket_number": "105 2485452619", "fare": 388, "taxes": [2.09, 7.5, 17.52, 1.53, 3.35, 5.99, 11.71, 5.83, 2.7, 5.83, 8.81], "issuing_airline_and_date": "FINNAIR 07Jan25", "iata": "38497745", "restrictions_endorsements": "Chng Foc/Non Ref", "fare_calculation": "ROM AY X/HEL AY OUL214.90AY X/HEL AY ROM193.53NUC408.43END ROE0.948998", "grand_total": 460.86, "baggage_allowance": [{"type": "1st Checked Bag", "weight": "23KGS", "charge": "Free of Charge"}, {"type": "CARRY-ON BAG", "weight": "8KG 18LB", "charge": "Free of Charge"}], "baggage_prohibited": "NOT APPLICABLE", "units": {"LB": "Weight In Pounds", "KG": "Weight In Kilos", "LI": "Linear Inches", "LCM": "Linear Centimeters", "MAX": "Maximum Allowed", "PC": "Number of Pieces"}, "special_notes": ["Baggage allowance and charges are provided for information only.", "Additional discounts may apply depending on advance purchase or Flyer-specific factors.", "Most carriers' e-tickets have expiration dates and conditions of use. Check the carrier's fare rules for more information.", "If the passengers journey involves an ultimate destination or stop in a country other than the country of departure, the Montreal or the Warsaw Convention may be applicable.", "Information about prohibited or restricted materials as baggage, baggage allowance and conditions of carriage and notices is available at any Finnair service point or at www.finnair.com.", "The carriage of certain hazardous materials, like aerosols, fireworks, and flammable liquids, aboard the aircraft is forbidden.", "Data Protection Notice: Your personal data will be processed in accordance with the applicable carrier's privacy policy.", "Flight must always be used in sequence as shown on the ticket. If you leave any part of your flights unused, the remaining flights will be automatically cancelled.", "You can check in online 36 hours before departure when your journey begins with a Finnair flight at www.finnair.com/checkin.", "If you use the self-service kiosk at the airport, please use the barcode from your boarding pass or e-ticket."]}, "compliance": {"validation_result": {"is_valid": false, "issues_count": 6, "issues": [{"issue_type": "Standards & Compliance | Fix Identified", "field": "customer_name_on_invoice", "description": "The customer name on the invoice is missing. It must show either 'Global People s.r.l.' or 'GoGlobal Consulting S.r.l.' as customer.", "recommendation": "It is recommended to address this issue with the supplier or provider to ensure the correct customer name is displayed.", "knowledge_base_reference": "Must show Global People s.r.l. as customer or GoGlobal Consulting S.r.l as customer"}, {"issue_type": "Standards & Compliance | Fix Identified", "field": "customer_address_on_invoice", "description": "The customer address on the invoice is missing. It must show either 'Via Venti Settembre 3, Torino (TO) CAP 10121, Italy' or 'Via <PERSON><PERSON> Di Modrone 38, 20122 Milano, Italia' as customer address.", "recommendation": "It is recommended to address this issue with the supplier or provider to ensure the correct customer address is displayed.", "knowledge_base_reference": "Must show Via Venti Settembre 3, Torino (TO) CAP 10121, Italy or Via Uberto <PERSON> Di Modrone 38, 20122 Milano, Italia"}, {"issue_type": "Standards & Compliance | Fix Identified", "field": "customer_vat_number_on_invoice", "description": "The customer VAT number on the invoice is missing. It must show either 'IT***********' or 'P.IVA 12205930964' as customer VAT number.", "recommendation": "It is recommended to address this issue with the supplier or provider to ensure the correct customer VAT number is displayed.", "knowledge_base_reference": "Must show IT*********** or P.IVA 12205930964"}, {"issue_type": "Standards & Compliance | Follow-up Action Identified", "field": "customer_tax_code_on_invoice", "description": "The customer tax code on the invoice is missing. It must show '***********' as customer tax code for Global People s.r.l.", "recommendation": "It is recommended to address this issue with the supplier or provider to ensure the correct customer tax code is displayed.", "knowledge_base_reference": "Must show ***********"}, {"issue_type": "Standards & Compliance | Follow-up Action Identified", "field": "receipt_quality", "description": "The receipt quality is not specified. It must be scanned, clear, and readable.", "recommendation": "Ensure the receipt is scanned and meets the required quality standards.", "knowledge_base_reference": "Must be scanned (not photos), clear and readable"}, {"issue_type": "Standards & Compliance | Follow-up Action Identified", "field": "business_trip_reporting", "description": "Business trip reporting is missing. A separate report must be submitted for each business trip.", "recommendation": "Submit a separate report for this business trip.", "knowledge_base_reference": "Submit separate report for each business trip"}], "corrected_receipt": null, "compliance_summary": "The receipt is not fully compliant with the required standards. Several mandatory fields are missing, and additional documentation is required. The issues need to be addressed with the supplier or provider to ensure compliance."}, "technical_details": {"content_type": "expense_receipt", "country": "Italy", "icp": "Global People", "receipt_type": "flights", "issues_count": 6}}, "citations": {"citations": {"currency": {"field_citation": {"source_text": "Receipt currency", "confidence": 1, "source_location": "requirements", "context": "Receipt_Type:\"All\", ICP_Specific?:\"No\", ICP_Name:\"All ICPs\", Mandatory/Optional:\"Mandatory\", Rule:\"Same currency with clear exchange rate\"", "match_type": "exact"}, "value_citation": {"source_text": "EUR", "confidence": 1, "source_location": "markdown", "context": "Fare : EUR 388.00", "match_type": "exact"}}, "amount": {"field_citation": {"source_text": "Expense amount", "confidence": 1, "source_location": "requirements", "context": "Receipt_Type:\"All\", ICP_Specific?:\"No\", ICP_Name:\"All ICPs\", Mandatory/Optional:\"Mandatory\", Rule:\"Must be clearly stated on receipt\"", "match_type": "exact"}, "value_citation": {"source_text": "460.86", "confidence": 1, "source_location": "markdown", "context": "Grand Total : EUR 460.86", "match_type": "exact"}}, "receipt_type": {"field_citation": {"source_text": "Type of supporting document", "confidence": 1, "source_location": "requirements", "context": "Receipt_Type:\"All\", ICP_Specific?:\"No\", ICP_Name:\"All ICPs\", Mandatory/Optional:\"Mandatory\", Rule:\"Must be actual tax receipts or invoices, not booking confirmations\"", "match_type": "exact"}, "value_citation": {"source_text": "Receipt", "confidence": 1, "source_location": "markdown", "context": "Receipt", "match_type": "exact"}}, "payment_method": {"field_citation": {"source_text": "Method of payment used", "confidence": 1, "source_location": "requirements", "context": "Receipt_Type:\"All\", ICP_Specific?:\"No\", ICP_Name:\"All ICPs\", Mandatory/Optional:\"Mandatory\", Rule:\"Must be traceable: bank transfers, postal transfers, credit/debit cards, prepaid cards, bank/cashier's checks\"", "match_type": "exact"}, "value_citation": {"source_text": "CC VI XXXXXXXXXXXX2982 ExpXXXX S385619", "confidence": 1, "source_location": "markdown", "context": "Form of payment : CC VI XXXXXXXXXXXX2982 ExpXXXX S385619", "match_type": "exact"}}, "fare": {"field_citation": {"source_text": "Expense amount", "confidence": 1, "source_location": "requirements", "context": "Receipt_Type:\"All\", ICP_Specific?:\"No\", ICP_Name:\"All ICPs\", Mandatory/Optional:\"Mandatory\", Rule:\"Must be clearly stated on receipt\"", "match_type": "exact"}, "value_citation": {"source_text": "388", "confidence": 1, "source_location": "markdown", "context": "Fare : EUR 388.00", "match_type": "exact"}}, "taxes": {"field_citation": {"source_text": "Taxes", "confidence": 1, "source_location": "requirements", "context": "No specific field type for taxes, but generally required in receipts", "match_type": "exact"}, "value_citation": {"source_text": "2.09, 7.5, 17.52, 1.53, 3.35, 5.99, 11.71, 5.83, 2.7, 5.83, 8.81", "confidence": 1, "source_location": "markdown", "context": "Taxes : EUR 2.09 EX EUR 7.50 HB EUR 17.52 IT EUR 1.53 MJ EUR 3.35 VT EUR 5.99 DQ EUR 11.71 QU EUR 5.83 WL EUR 2.70 XU EUR 5.83 ZX EUR 8.81 DV", "match_type": "exact"}}, "issuing_airline_and_date": {"field_citation": {"source_text": "Issuing Airline and date", "confidence": 1, "source_location": "markdown", "context": "Issuing Airline and date : FINNAIR 07Jan25", "match_type": "exact"}, "value_citation": {"source_text": "FINNAIR 07Jan25", "confidence": 1, "source_location": "markdown", "context": "Issuing Airline and date : FINNAIR 07Jan25", "match_type": "exact"}}, "iata": {"field_citation": {"source_text": "IATA", "confidence": 1, "source_location": "markdown", "context": "IATA : 38497745", "match_type": "exact"}, "value_citation": {"source_text": "38497745", "confidence": 1, "source_location": "markdown", "context": "IATA : 38497745", "match_type": "exact"}}, "restrictions_endorsements": {"field_citation": {"source_text": "Restriction(s)/Endorsements", "confidence": 1, "source_location": "markdown", "context": "Restriction(s)/Endorsements : Chng Foc/Non Ref", "match_type": "exact"}, "value_citation": {"source_text": "Chng Foc/Non Ref", "confidence": 1, "source_location": "markdown", "context": "Restriction(s)/Endorsements : Chng Foc/Non Ref", "match_type": "exact"}}, "fare_calculation": {"field_citation": {"source_text": "Fare Calculation", "confidence": 1, "source_location": "markdown", "context": "Fare Calculation : ROM AY X/HEL AY OUL214.90AY X/HEL AY ROM193.53NUC408.43END ROE0.948998", "match_type": "exact"}, "value_citation": {"source_text": "ROM AY X/HEL AY OUL214.90AY X/HEL AY ROM193.53NUC408.43END ROE0.948998", "confidence": 1, "source_location": "markdown", "context": "Fare Calculation : ROM AY X/HEL AY OUL214.90AY X/HEL AY ROM193.53NUC408.43END ROE0.948998", "match_type": "exact"}}, "grand_total": {"field_citation": {"source_text": "Grand Total", "confidence": 1, "source_location": "markdown", "context": "Grand Total : EUR 460.86", "match_type": "exact"}, "value_citation": {"source_text": "460.86", "confidence": 1, "source_location": "markdown", "context": "Grand Total : EUR 460.86", "match_type": "exact"}}}, "metadata": {"total_fields_analyzed": 19, "fields_with_field_citations": 11, "fields_with_value_citations": 11, "average_confidence": 1}}, "timing": {"phase_timings": {"markdown_extraction_seconds": "7.4", "image_quality_assessment_seconds": "4.0", "file_classification_seconds": "4.1", "data_extraction_seconds": "13.0", "issue_detection_seconds": "17.6", "citation_generation_seconds": "30.7"}, "agent_performance": {"markdown_extraction": {"start_time": "2025-07-31T18:39:55.932Z", "end_time": "2025-07-31T18:40:03.293Z", "duration_seconds": "7.4", "document_reader_used": "textract"}, "image_quality_assessment": {"start_time": "2025-07-31T18:40:03.294Z", "end_time": "2025-07-31T18:40:07.277Z", "duration_seconds": "4.0", "model_used": "eu.amazon.nova-pro-v1:0", "execution_mode": "parallel"}, "file_classification": {"start_time": "2025-07-31T18:40:03.295Z", "end_time": "2025-07-31T18:40:07.418Z", "duration_seconds": "4.1", "model_used": "eu.amazon.nova-pro-v1:0", "execution_mode": "parallel"}, "data_extraction": {"start_time": "2025-07-31T18:40:03.296Z", "end_time": "2025-07-31T18:40:16.263Z", "duration_seconds": "13.0", "model_used": "eu.amazon.nova-pro-v1:0", "execution_mode": "parallel"}, "issue_detection": {"start_time": "2025-07-31T18:40:16.264Z", "end_time": "2025-07-31T18:40:33.907Z", "duration_seconds": "17.6", "model_used": "eu.amazon.nova-pro-v1:0", "execution_mode": "parallel"}, "citation_generation": {"start_time": "2025-07-31T18:40:16.265Z", "end_time": "2025-07-31T18:40:46.946Z", "duration_seconds": "30.7", "model_used": "eu.amazon.nova-pro-v1:0", "execution_mode": "parallel"}}, "total_processing_time_seconds": "51.0", "performance_metrics": {"parallel_group_1_seconds": "13.0", "parallel_group_2_seconds": "30.7", "total_parallel_time_seconds": "43.7", "estimated_sequential_time_seconds": "76.8", "estimated_speedup_factor": "1.76"}, "validation": {"total_time_seconds": "51.0", "expected_parallel_time_seconds": "51.1", "sequential_sum_seconds": "76.8", "difference_seconds": "0.1", "is_consistent": true, "tolerance_seconds": "3.0", "processing_mode": "parallel", "time_saved_seconds": "25.8"}}, "metadata": {"filename": "Copy of italia_file_6.pdf", "processing_time": 51015, "country": "Italy", "icp": "Global People", "processed_at": "2025-07-31T18:40:46.947Z", "optimization": {"parallel_processing": true, "parallel_group_1_duration_seconds": "13.0", "parallel_group_2_duration_seconds": "30.7", "estimated_sequential_time_seconds": "76.8", "actual_parallel_time_seconds": "51.0"}}}